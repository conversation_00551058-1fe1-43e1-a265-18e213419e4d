#!/bin/bash

# Script to monitor enrollment status and trigger registration when seats become available
# Usage: ./monitor_enrollment.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# Function to check enrollment status
check_enrollment() {
    local response
    response=$(curl -s 'https://registration.banner.gatech.edu/StudentRegistrationSsb/ssb/searchResults/getEnrollmentInfo' \
        -X 'POST' \
        -H 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8' \
        -H 'Accept: text/html, */*; q=0.01' \
        -H 'Sec-Fetch-Site: same-origin' \
        -H 'Accept-Language: en-US,en;q=0.9' \
        -H 'Accept-Encoding: gzip, deflate, br' \
        -H 'Sec-Fetch-Mode: cors' \
        -H 'Origin: https://registration.banner.gatech.edu' \
        -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15' \
        -H 'Content-Length: 39' \
        -H 'Referer: https://registration.banner.gatech.edu/StudentRegistrationSsb/ssb/classRegistration/classRegistration' \
        -H 'Connection: keep-alive' \
        -H 'Sec-Fetch-Dest: empty' \
        -H 'X-Requested-With: XMLHttpRequest' \
        -H 'Priority: u=3, i' \
        --data 'term=202508&courseReferenceNumber=86196' 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_error "Failed to fetch enrollment data"
        return 1
    fi
    
    echo "$response"
}

# Function to parse enrollment seats available from HTML response
get_seats_available() {
    local response="$1"
    # Extract the number after "Enrollment Seats Available:" from HTML
    local seats

    # Method 1: Look for the pattern and extract the number from the next span
    seats=$(echo "$response" | grep -A2 "Enrollment Seats Available:" | grep -o 'dir="ltr">[0-9]*<' | sed 's/dir="ltr">//;s/<.*//' | head -1)

    # Method 2: Use sed to extract the number directly
    if [ -z "$seats" ]; then
        seats=$(echo "$response" | sed -n '/Enrollment Seats Available:/,/<span/{s/.*dir="ltr">\([0-9]*\)<.*/\1/p}' | head -1)
    fi

    # Method 3: Simple grep and extract
    if [ -z "$seats" ]; then
        seats=$(echo "$response" | grep -A3 "Enrollment Seats Available:" | grep -o '[0-9]\+' | head -1)
    fi

    # Default to 0 if we can't parse it
    if [ -z "$seats" ] || ! [[ "$seats" =~ ^[0-9]+$ ]]; then
        seats=0
    fi

    echo "$seats"
}

# Function to trigger registration
trigger_registration() {
    log "🚀 Seats available! Triggering registration..."
    
    if [ -f "./register_course.sh" ]; then
        chmod +x ./register_course.sh
        ./register_course.sh
        local exit_code=$?
        
        if [ $exit_code -eq 0 ]; then
            log_success "✅ Registration script executed successfully!"
        else
            log_error "❌ Registration script failed with exit code $exit_code"
        fi
        
        return $exit_code
    else
        log_error "❌ Registration script 'register_course.sh' not found!"
        return 1
    fi
}

# Function to generate random delay between 1-5 seconds
random_delay() {
    local delay=$((RANDOM % 5 + 1))
    echo $delay
}

# Main monitoring loop
main() {
    log "🔍 Starting enrollment monitoring for CRN 86196..."
    log "📊 Checking every 1-5 seconds (random interval)"
    log "🛑 Press Ctrl+C to stop"
    echo
    
    local check_count=0
    
    while true; do
        check_count=$((check_count + 1))
        
        # Get enrollment data
        local response
        response=$(check_enrollment)
        
        if [ $? -eq 0 ]; then
            # Parse seats available
            local seats_available
            seats_available=$(get_seats_available "$response")
            
            log "Check #$check_count - Seats Available: $seats_available"
            
            # Check if seats are available (> 0)
            if [ "$seats_available" -gt 0 ]; then
                log_success "🎉 SEATS AVAILABLE: $seats_available"
                
                # Trigger registration
                if trigger_registration; then
                    log_success "✅ Registration process completed successfully!"
                    exit 0
                else
                    log_error "❌ Registration process failed!"
                    exit 1
                fi
            fi
        else
            log_warning "⚠️  Check #$check_count - Failed to get enrollment data"
        fi
        
        # Random delay between 1-5 seconds
        local delay
        delay=$(random_delay)
        sleep $delay
    done
}

# Handle Ctrl+C gracefully
trap 'log_warning "🛑 Monitoring stopped by user"; exit 0' INT

# Run main function
main
