# Course Registration Monitor

This project contains scripts to automatically monitor course enrollment and register when seats become available.

## Files

- `monitor_enrollment.sh` - Main monitoring script that checks enrollment status every 1-5 seconds
- `register_course.sh` - Registration script that gets triggered when seats are available

## Usage

1. **Make sure both scripts are executable:**
   ```bash
   chmod +x monitor_enrollment.sh register_course.sh
   ```

2. **Start monitoring:**
   ```bash
   ./monitor_enrollment.sh
   ```

3. **The script will:**
   - Check enrollment status every 1-5 seconds (random interval)
   - Display current status with timestamps
   - Automatically trigger registration when seats become available (> 0)
   - Exit after successful registration attempt

## What it monitors

- **Course:** CS 6515 - Intro to Grad Algorithms
- **CRN:** 86196
- **Term:** 202508

## Output

The monitor script provides colored output:
- 🔍 Blue: General information
- ✅ Green: Success messages
- ⚠️ Yellow: Warnings
- ❌ Red: Errors

Example output:
```
🔍 Starting enrollment monitoring for CRN 86196...
📊 Checking every 1-5 seconds (random interval)
🛑 Press Ctrl+C to stop

[2025-08-21 10:30:15] Check #1 - Seats Available: 0
[2025-08-21 10:30:18] Check #2 - Seats Available: 0
[2025-08-21 10:30:22] Check #3 - Seats Available: 1
[2025-08-21 10:30:22] 🎉 SEATS AVAILABLE: 1
[2025-08-21 10:30:22] 🚀 Seats available! Triggering registration...
```

## Important Notes

⚠️ **Session Management:** The registration script uses hardcoded session cookies and tokens. These may expire and need to be updated for the registration to work properly.

⚠️ **Rate Limiting:** The script uses random delays (1-5 seconds) to avoid overwhelming the server.

⚠️ **Testing:** Test the scripts in a safe environment first to ensure they work as expected.

## Stopping the Monitor

Press `Ctrl+C` to stop the monitoring script at any time.

## Logs

When registration is triggered, the response is saved to `registration_response.log` for debugging purposes.
