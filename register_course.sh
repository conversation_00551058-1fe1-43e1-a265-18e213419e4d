#!/bin/bash

# Script to register for the course when seats become available
# This script is triggered by monitor_enrollment.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# Function to attempt course registration
register_course() {
    log "🎯 Attempting to register for CS 6515 (CRN: 86196)..."
    
    local response
    response=$(curl -s 'https://registration.banner.gatech.edu/StudentRegistrationSsb/ssb/classRegistration/submitRegistration/batch' \
        -X 'POST' \
        -H 'Content-Type: application/json' \
        -H 'Accept: application/json, text/javascript, */*; q=0.01' \
        -H 'Sec-Fetch-Site: same-origin' \
        -H 'Accept-Language: en-US,en;q=0.9' \
        -H 'Accept-Encoding: gzip, deflate, br' \
        -H 'Sec-Fetch-Mode: cors' \
        -H 'Origin: https://registration.banner.gatech.edu' \
        -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Safari/605.1.15' \
        -H 'Content-Length: 9167' \
        -H 'Referer: https://registration.banner.gatech.edu/StudentRegistrationSsb/ssb/classRegistration/classRegistration' \
        -H 'Connection: keep-alive' \
        -H 'Sec-Fetch-Dest: empty' \
        -H 'Cookie: table1-width=112+113+30+73+30+30+30+98+194+30+71+57+181+98+7+20; JSESSIONID=1EACE0D03ECA7107760164B514E78D1F; BIGipServer~BANNER~registration.coda=2611138434.64288.0000; _ga=GA1.1.406112552.1753596936; _ga_DBY1MDQHS1=GS2.1.s1755787426$o9$g1$t1755787437$j49$l0$h0; _ga_GP3B01FPCR=GS2.1.s1754675284$o3$g1$t1754675327$j17$l0$h0; fpestid=cBP_A7iPshSsJMFWk9M1HtupXwmwHP00Q8WGTmuyDhfL-xOD6nNmFdIcKB_Ih_YUpK5MXg; lt-anonymous-id="0.6c385d22198541a6440"; _ga_30TT54P0XE=GS2.1.s1753755191$o1$g1$t1753757473$j60$l0$h0; _ga_L3KDESR6P8=GS2.1.s1753756494$o1$g1$t1753756832$j59$l0$h0; _hjSessionUser_3867652=eyJpZCI6ImUwYTI3MWUzLTZhYzctNTdiZi1iY2ZkLTIwZjI0OTU0NWVjOSIsImNyZWF0ZWQiOjE3NTM3NTY0OTQ2MDIsImV4aXN0aW5nIjp0cnVlfQ==; _clck=y7qsuq%7C2%7Cfy0%7C0%7C2034; _ga_NXJS7SXPTB=GS2.1.s1753755191$o1$g0$t1753755193$j58$l0$h0; _ga_FLTL39B1PG=GS2.1.s1753596936$o1$g0$t1753598309$j60$l0$h0' \
        -H 'X-Requested-With: XMLHttpRequest' \
        -H 'X-Synchronizer-Token: b95a7462-c88c-4441-ae23-5e8f19d4b843' \
        -H 'Priority: u=3, i' \
        --data-raw '{"create":[],"update":[{"addAuthorizationCrnMessage":null,"addAuthorizationCrnStatus":"INCOMPLETE","addDate":"08/21/2025","approvalOverride":"N","approvalReceivedIndicator":null,"approvalReceivedIndicatorHold":null,"attached":true,"attemptedHours":0,"authorizationCode":null,"billHour":3,"billHourInitial":3,"billHours":{"class":"net.hedtech.banner.student.registration.RegistrationCreditHour","creditHourHigh":null,"creditHourIndicator":null,"creditHourList":null,"creditHourLow":null},"block":null,"blockPermitOverride":null,"blockRuleSequenceNumber":null,"campus":"O","campusOverride":"N","capcOverride":"N","censusEnrollmentDate":"10/07/2025","class":"net.hedtech.banner.student.registration.RegistrationTemporaryView","classOverride":"N","cohortOverride":"N","collegeOverride":"N","completionDate":"12/11/2025","corqOverride":"N","courseAlias":null,"courseContinuingEducationIndicator":"N","courseDisplay":"6515","courseNumber":"6515","courseReferenceNumber":"86196","courseRegistrationStatus":"RW","courseRegistrationStatusDescription":"**Registered (Web)","courseTitle":"Intro to Grad Algorithms","creditHour":3,"creditHourInitial":3,"creditHours":{"class":"net.hedtech.banner.student.registration.RegistrationCreditHour","creditHourHigh":null,"creditHourIndicator":null,"creditHourList":null,"creditHourLow":null},"crnErrors":[{"class":"net.hedtech.banner.student.registration.RegistrationMessage","errorFlag":"F","message":"Closed - 0 Waitlisted ","messageType":"WAIT"}],"dataOrigin":"Banner","degreeOverride":"N","departmentOverride":"N","dirty":false,"dirtyPropertyNames":[],"duplicateOverride":"N","durationUnit":null,"errorFlag":"F","errorLink":null,"errors":{"errors":[]},"grade":null,"gradeComment":null,"gradeDate":null,"gradeMid":null,"gradingMode":"L","gradingModeDescription":"Letter Grade","id":30693478,"instructionalMethodDescription":"Fully at a Distance (BOR)","lastModified":"08/21/2025","level":"GS","levelDescription":"Graduate Semester","levelOverride":"N","linkOverride":"N","majorOverride":"N","maxEnrollment":600,"message":"Closed - 0 Waitlisted ","messageType":"WAIT","mexcOverride":"N","newBlock":null,"newBlockRuleSequenceNumber":null,"numberOfUnits":null,"originalCourseRegistrationStatus":null,"originalRecordStatus":"N","originalVoiceResponseStatusType":null,"overrideDurationIndicator":false,"partOfTerm":"1","partOfTermDescription":"Full Term Session","permitOverrideUpdate":null,"preqOverride":"N","programOverride":"N","properties":{"billHourInitial":3,"registrationToDate":null,"errorFlag":null,"reservedKey":null,"courseContinuingEducationIndicator":"N","messageType":null,"subjectDescription":"Computer Science","courseRegistrationStatus":"RW","studentAttributeOverride":"N","approvalOverride":"N","classOverride":"N","selectedLevel":{"class":"net.hedtech.banner.student.registration.RegistrationLevel","description":null,"level":null},"campusOverride":"N","gradingMode":"L","selectedCreditHour":null,"block":null,"gradeComment":null,"creditHourInitial":3,"sequenceNumber":"O01","courseRegistrationStatusDescription":"**Registered (Web)","tuitionWaiverIndicator":"N","repeatOverride":"N","preqOverride":"N","creditHour":3,"majorOverride":"N","courseReferenceNumber":"86196","creditHours":{"class":"net.hedtech.banner.student.registration.RegistrationCreditHour","creditHourHigh":null,"creditHourIndicator":null,"creditHourList":null,"creditHourLow":null},"structuredRegistrationHeaderSequence":null,"scheduleType":"A","approvalReceivedIndicator":null,"grade":null,"programOverride":"N","startDate":"08/18/2025","addAuthorizationCrnMessage":null,"originalCourseRegistrationStatus":null,"registrationLevels":[],"authorizationCode":null,"attemptedHours":0,"selectedGradingMode":{"class":"net.hedtech.banner.student.registration.RegistrationGradingMode","description":null,"gradingMode":null},"waivHour":3,"levelDescription":"Graduate Semester","selectedStartEndDate":{"class":"net.hedtech.banner.student.registration.RegistrationOlrStartEndDate","courseReferenceNumber":null,"durationUnit":null,"durationUnitDescription":null,"endDate":null,"numberOfUnits":null,"overrideDurationIndicator":null,"registrationDate":null,"sectionEndFromDate":null,"sectionEndToDate":null,"sectionStartFromDate":null,"sectionStartToDate":null,"startDate":null,"systemIn":null},"selectedOverride":null,"originalRecordStatus":"N","testOverride":"N","submitResultIndicator":null,"dataOrigin":"Banner","term":"202508","mexcOverride":"N","registrationOverrides":[],"levelOverride":"N","structuredRegistrationDetailSequence":null,"registrationFromDate":null,"waitCapacity":999,"blockRuleSequenceNumber":null,"sessionId":null,"billHours":{"class":"net.hedtech.banner.student.registration.RegistrationCreditHour","creditHourHigh":null,"creditHourIndicator":null,"creditHourList":null,"creditHourLow":null},"addDate":"08/21/2025","blockPermitOverride":null,"studyPathName":null,"addAuthorizationCrnStatus":"INCOMPLETE","studyPathKeySequence":null,"recordStatus":"N","numberOfUnits":null,"scheduleDescription":"Lecture*","timeStatusHours":0,"selectedBillHour":null,"courseDisplay":"6515","gradeDate":null,"registrationActions":[{"class":"net.hedtech.banner.student.registration.RegistrationAction","courseRegistrationStatus":"RW","description":"**Registered (Web)","registrationStatusDate":null,"remove":false,"subActions":null,"voiceType":"R"},{"class":"net.hedtech.banner.student.registration.RegistrationAction","courseRegistrationStatus":"internal-remove","description":"Remove","registrationStatusDate":null,"remove":true,"subActions":null,"voiceType":null}],"errorLink":null,"maxEnrollment":600,"statusIndicator":"P","subject":"CS","removeIndicator":null,"partOfTerm":"1","billHour":3,"sectionCourseTitle":"Intro to Grad Algorithms","overrideDurationIndicator":false,"courseTitle":"Intro to Grad Algorithms","duplicateOverride":"N","courseAlias":null,"durationUnit":null,"censusEnrollmentDate":"10/07/2025","corqOverride":"N","gradeMid":null,"level":"GS","instructionalMethodDescription":"Fully at a Distance (BOR)","campus":"O","degreeOverride":"N","waitOverride":"N","newBlock":null,"rpthOverride":"N","newBlockRuleSequenceNumber":null,"gradingModeDescription":"Letter Grade","partOfTermDescription":"Full Term Session","lastModified":"08/21/2025","timeOverride":"N","registrationAuthorizationActiveCode":null,"linkOverride":"N","registrationStatusDate":"08/21/2025","departmentOverride":"N","crnErrors":[],"selectedStudyPath":{"class":"net.hedtech.banner.student.registration.RegistrationStudyPath","description":null,"keySequenceNumber":null},"registrationGradingModes":[{"class":"net.hedtech.banner.student.registration.RegistrationGradingMode","description":"Letter Grade","gradingMode":"L"}],"approvalReceivedIndicatorHold":null,"capcOverride":"N","specialApproval":null,"courseNumber":"6515","message":null,"cohortOverride":"N","statusDescription":"Pending","selectedAction":null,"collegeOverride":"N","originalVoiceResponseStatusType":null,"completionDate":"12/11/2025","permitOverrideUpdate":null,"voiceResponseStatusType":"R","registrationStudyPaths":[]},"recordStatus":"N","registrationActions":[{"class":"net.hedtech.banner.student.registration.RegistrationAction","courseRegistrationStatus":"internal-remove","description":"Remove","registrationStatusDate":null,"remove":true,"subActions":null,"voiceType":null},{"class":"net.hedtech.banner.student.registration.RegistrationAction","courseRegistrationStatus":"RW","description":"**Registered (Web)","registrationStatusDate":null,"remove":false,"subActions":null,"voiceType":"R"}],"registrationAuthorizationActiveCode":null,"registrationFromDate":null,"registrationGradingModes":[],"registrationLevels":[],"registrationOverrides":[],"registrationStatusDate":"08/21/2025","registrationStudyPaths":[],"registrationToDate":null,"removeIndicator":"Y","repeatOverride":"N","reservedKey":null,"rpthOverride":"N","scheduleDescription":"Lecture*","scheduleType":"A","sectionCourseTitle":"Intro to Grad Algorithms","selectedAction":"RW","selectedBillHour":null,"selectedCreditHour":null,"selectedGradingMode":{"class":"net.hedtech.banner.student.registration.RegistrationGradingMode","description":null,"gradingMode":null},"selectedLevel":{"class":"net.hedtech.banner.student.registration.RegistrationLevel","description":null,"level":null},"selectedOverride":null,"selectedStartEndDate":null,"selectedStudyPath":{"class":"net.hedtech.banner.student.registration.RegistrationStudyPath","description":null,"keySequenceNumber":null},"sequenceNumber":"O01","sessionId":null,"specialApproval":null,"startDate":"08/18/2025","statusDescription":"Errors Preventing Registration","statusIndicator":"F","structuredRegistrationDetailSequence":null,"structuredRegistrationHeaderSequence":null,"studentAttributeOverride":"N","studyPathKeySequence":null,"studyPathName":null,"subject":"CS","subjectDescription":"Computer Science","submitResultIndicator":"F","term":"202508","testOverride":"N","timeOverride":"N","timeStatusHours":0,"tuitionWaiverIndicator":"N","version":0,"voiceResponseStatusType":"R","waitCapacity":null,"waitOverride":"N","waivHour":3}],"destroy":[],"uniqueSessionId":"2rkhk1755787448528"}' 2>/dev/null)
    
    local curl_exit_code=$?
    
    if [ $curl_exit_code -ne 0 ]; then
        log_error "❌ Failed to send registration request (curl exit code: $curl_exit_code)"
        return 1
    fi
    
    # Log the response for debugging
    log "📋 Registration response received"
    echo "$response" > registration_response.log
    log "📄 Full response saved to registration_response.log"
    
    # Check if the response indicates success
    # This is a basic check - you may need to adjust based on actual response format
    if echo "$response" | grep -q "success\|registered\|enrolled" 2>/dev/null; then
        log_success "✅ Registration appears successful!"
        return 0
    elif echo "$response" | grep -q "error\|failed\|denied" 2>/dev/null; then
        log_error "❌ Registration appears to have failed"
        return 1
    else
        log "⚠️  Registration status unclear - check registration_response.log"
        return 0
    fi
}

# Main function
main() {
    log "🚀 Starting course registration process..."
    
    if register_course; then
        log_success "✅ Registration process completed!"
        exit 0
    else
        log_error "❌ Registration process failed!"
        exit 1
    fi
}

# Run main function
main
